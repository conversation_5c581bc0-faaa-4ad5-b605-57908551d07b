import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Provider } from 'react-redux'
import { store } from './store/store'
import HomePage from './pages/HomePage'
import TodoDetailPage from './pages/TodoDetailPage'

function App() {
  return (
    <Provider store={store}>
      <Router>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/todos/:id" element={<TodoDetailPage />} />
        </Routes>
      </Router>
    </Provider>
  )
}

export default App
