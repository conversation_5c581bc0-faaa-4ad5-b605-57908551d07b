import { Link } from 'react-router-dom'

const TodoCard = ({ todo, onDelete, onToggleComplete }) => {
  return (
    <div style={{ border: '1px solid #ccc', padding: '15px', margin: '10px 0' }}>
      <div>
        <h3 style={{ textDecoration: todo.completed ? 'line-through' : 'none' }}>
          {todo.title}
        </h3>
        <span style={{ fontSize: '12px', background: '#f0f0f0', padding: '2px 5px' }}>
          {(todo.priority || 'normal').toUpperCase()}
        </span>
      </div>

      <p style={{ textDecoration: todo.completed ? 'line-through' : 'none', margin: '10px 0' }}>
        {todo.description || 'No description provided.'}
      </p>

      <div style={{ margin: '10px 0' }}>
        <button
          onClick={() => onToggleComplete(todo)}
          style={{ marginRight: '5px' }}
        >
          {todo.completed ? 'Undo' : 'Complete'}
        </button>

        <Link
          to={`/todos/${todo.id}`}
          style={{ marginRight: '5px', textDecoration: 'none' }}
        >
          <button>View</button>
        </Link>

        <button
          onClick={() => onDelete(todo.id)}
        >
          Delete
        </button>
      </div>

      <div style={{ fontSize: '12px', color: '#666' }}>
        Created: {new Date(todo.createdAt).toLocaleDateString()}
      </div>
    </div>
  )
}

export default TodoCard
