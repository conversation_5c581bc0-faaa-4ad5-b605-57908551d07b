import { useState } from 'react'

const TodoForm = ({ onSubmit, loading }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    priority: 'medium'
  })

  const handleSubmit = async (e) => {
    e.preventDefault()
    if (!formData.title.trim()) return

    const result = await onSubmit(formData)
    if (result.success) {
      setFormData({
        title: '',
        description: '',
        priority: 'medium'
      })
    }
  }

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <form onSubmit={handleSubmit} style={{ border: '1px solid #ccc', padding: '20px', marginBottom: '20px' }}>
      <h2>Add New Todo</h2>

      <div style={{ marginBottom: '10px' }}>
        <label htmlFor="title">Title *</label>
        <input
          type="text"
          id="title"
          name="title"
          value={formData.title}
          onChange={handleChange}
          placeholder="Enter todo title..."
          required
          style={{ width: '100%', padding: '5px', marginTop: '5px' }}
        />
      </div>

      <div style={{ marginBottom: '10px' }}>
        <label htmlFor="description">Description</label>
        <textarea
          id="description"
          name="description"
          value={formData.description}
          onChange={handleChange}
          placeholder="Enter todo description..."
          style={{ width: '100%', padding: '5px', marginTop: '5px', height: '60px' }}
        />
      </div>

      <div style={{ marginBottom: '10px' }}>
        <label htmlFor="priority">Priority</label>
        <select
          id="priority"
          name="priority"
          value={formData.priority}
          onChange={handleChange}
          style={{ width: '100%', padding: '5px', marginTop: '5px' }}
        >
          <option value="low">Low</option>
          <option value="medium">Medium</option>
          <option value="high">High</option>
        </select>
      </div>

      <button
        type="submit"
        disabled={loading || !formData.title.trim()}
        style={{ width: '100%', padding: '10px' }}
      >
        {loading ? 'Adding...' : 'Add Todo'}
      </button>
    </form>
  )
}

export default TodoForm
