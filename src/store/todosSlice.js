import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import axios from 'axios'

const API_URL = 'https://67cd350bdd7651e464eda2a9.mockapi.io/todos'

// Configure axios with timeout
const apiClient = axios.create({
  timeout: 10000, // 10 seconds timeout
  headers: {
    'Content-Type': 'application/json',
  }
})

// Async thunks
export const fetchTodos = createAsyncThunk(
  'todos/fetchTodos',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiClient.get(API_URL)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message)
    }
  }
)

export const addTodo = createAsyncThunk(
  'todos/addTodo',
  async (todoData, { rejectWithValue }) => {
    try {
      const newTodo = {
        ...todoData,
        completed: false,
        createdAt: new Date().toISOString()
      }
      const response = await apiClient.post(API_URL, newTodo)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message)
    }
  }
)

export const deleteTodo = createAsyncThunk(
  'todos/deleteTodo',
  async (todoId, { rejectWithValue }) => {
    try {
      await apiClient.delete(`${API_URL}/${todoId}`)
      return todoId
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message)
    }
  }
)

export const updateTodo = createAsyncThunk(
  'todos/updateTodo',
  async ({ id, updates, currentTodo }, { rejectWithValue }) => {
    try {
      // For MockAPI, we need to send the complete object with PUT
      const updatedTodo = {
        ...currentTodo,
        ...updates
      }
      console.log('Updating todo:', { id, updatedTodo })
      const response = await apiClient.put(`${API_URL}/${id}`, updatedTodo)
      console.log('Update response:', response.data)
      return response.data
    } catch (error) {
      console.error('Update error:', error)
      return rejectWithValue(error.response?.data || error.message)
    }
  }
)

const todosSlice = createSlice({
  name: 'todos',
  initialState: {
    items: [],
    loading: false,
    error: null,
  },
  reducers: {
    clearError: (state) => {
      state.error = null
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch todos
      .addCase(fetchTodos.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchTodos.fulfilled, (state, action) => {
        state.loading = false
        state.items = action.payload
      })
      .addCase(fetchTodos.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload
      })
      // Add todo
      .addCase(addTodo.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(addTodo.fulfilled, (state, action) => {
        state.loading = false
        state.items.push(action.payload)
      })
      .addCase(addTodo.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload
      })
      // Delete todo
      .addCase(deleteTodo.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(deleteTodo.fulfilled, (state, action) => {
        state.loading = false
        state.items = state.items.filter(todo => todo.id.toString() !== action.payload.toString())
      })
      .addCase(deleteTodo.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload
      })
      // Update todo
      .addCase(updateTodo.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updateTodo.fulfilled, (state, action) => {
        state.loading = false
        console.log('updateTodo.fulfilled - Looking for todo with ID:', action.payload.id)
        console.log('Current todos:', state.items.map(t => ({ id: t.id, title: t.title })))
        const index = state.items.findIndex(todo => todo.id.toString() === action.payload.id.toString())
        console.log('Found index:', index)
        if (index !== -1) {
          console.log('Updating todo at index:', index)
          state.items[index] = action.payload
        } else {
          console.error('Todo not found in state for update!')
        }
      })
      .addCase(updateTodo.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload
      })
  },
})

export const { clearError } = todosSlice.actions
export default todosSlice.reducer
