import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import axios from 'axios'

const API_URL = 'https://67cd350bdd7651e464eda2a9.mockapi.io/todos'

// Async thunks
export const fetchTodos = createAsyncThunk(
  'todos/fetchTodos',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(API_URL)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message)
    }
  }
)

export const addTodo = createAsyncThunk(
  'todos/addTodo',
  async (todoData, { rejectWithValue }) => {
    try {
      const newTodo = {
        ...todoData,
        completed: false,
        createdAt: new Date().toISOString()
      }
      const response = await axios.post(API_URL, newTodo)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message)
    }
  }
)

export const deleteTodo = createAsyncThunk(
  'todos/deleteTodo',
  async (todoId, { rejectWithValue }) => {
    try {
      await axios.delete(`${API_URL}/${todoId}`)
      return todoId
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message)
    }
  }
)

export const updateTodo = createAsyncThunk(
  'todos/updateTodo',
  async ({ id, updates }, { rejectWithValue }) => {
    try {
      const response = await axios.patch(`${API_URL}/${id}`, updates)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message)
    }
  }
)

const todosSlice = createSlice({
  name: 'todos',
  initialState: {
    items: [],
    loading: false,
    error: null,
  },
  reducers: {
    clearError: (state) => {
      state.error = null
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch todos
      .addCase(fetchTodos.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchTodos.fulfilled, (state, action) => {
        state.loading = false
        state.items = action.payload
      })
      .addCase(fetchTodos.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload
      })
      // Add todo
      .addCase(addTodo.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(addTodo.fulfilled, (state, action) => {
        state.loading = false
        state.items.push(action.payload)
      })
      .addCase(addTodo.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload
      })
      // Delete todo
      .addCase(deleteTodo.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(deleteTodo.fulfilled, (state, action) => {
        state.loading = false
        state.items = state.items.filter(todo => todo.id.toString() !== action.payload.toString())
      })
      .addCase(deleteTodo.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload
      })
      // Update todo
      .addCase(updateTodo.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updateTodo.fulfilled, (state, action) => {
        state.loading = false
        const index = state.items.findIndex(todo => todo.id.toString() === action.payload.id.toString())
        if (index !== -1) {
          state.items[index] = action.payload
        }
      })
      .addCase(updateTodo.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload
      })
  },
})

export const { clearError } = todosSlice.actions
export default todosSlice.reducer
