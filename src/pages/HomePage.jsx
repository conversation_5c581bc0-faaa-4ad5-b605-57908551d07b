import { useTodoAPI } from '../hooks/useTodoAPI'
import TodoCard from '../components/TodoCard'
import TodoForm from '../components/TodoForm'
import LoadingSpinner from '../components/LoadingSpinner'

const HomePage = () => {
  const {
    todos,
    loading,
    error,
    addTodo,
    deleteTodo,
    toggleComplete,
    clearError
  } = useTodoAPI()

  const handleAddTodo = async (todoData) => {
    return await addTodo(todoData)
  }

  const handleDeleteTodo = async (todoId) => {
    if (window.confirm('Are you sure you want to delete this todo?')) {
      return await deleteTodo(todoId)
    }
  }

  const handleToggleComplete = async (todo) => {
    return await toggleComplete(todo)
  }

  if (loading && todos.length === 0) {
    return <LoadingSpinner text="Loading todos..." />
  }

  return (
    <div>
      <h1>Todo List Manager</h1>
      <p>Manage your tasks efficiently with Redux & React</p>

      {error && (
        <div style={{ background: '#ffebee', color: '#c62828', padding: '10px', margin: '10px 0', border: '1px solid #ef5350' }}>
          <span>Error: {error}</span>
          <button
            onClick={clearError}
            style={{ float: 'right', background: 'none', border: 'none', cursor: 'pointer' }}
          >
            ×
          </button>
        </div>
      )}

      <div style={{ display: 'flex', gap: '20px' }}>
        {/* Form Section */}
        <div style={{ flex: '1' }}>
          <TodoForm onSubmit={handleAddTodo} loading={loading} />
        </div>

        {/* Todos Section */}
        <div style={{ flex: '2' }}>
          <h2>
            Your Todos ({todos.length})
            {loading && <span> (Loading...)</span>}
          </h2>

          {todos.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <div style={{ fontSize: '48px' }}>📝</div>
              <h3>No todos yet</h3>
              <p>Add your first todo to get started!</p>
            </div>
          ) : (
            <div>
              {todos.map((todo) => (
                <TodoCard
                  key={todo.id}
                  todo={todo}
                  onDelete={handleDeleteTodo}
                  onToggleComplete={handleToggleComplete}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default HomePage
