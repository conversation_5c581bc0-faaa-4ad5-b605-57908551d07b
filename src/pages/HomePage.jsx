import { useTodoAPI } from '../hooks/useTodoAPI'
import TodoCard from '../components/TodoCard'
import TodoForm from '../components/TodoForm'

const HomePage = () => {
  const {
    todos,
    loading,
    error,
    addTodo,
    deleteTodo,
    toggleComplete,
    clearError
  } = useTodoAPI()

  const handleAddTodo = async (todoData) => {
    return await addTodo(todoData)
  }

  const handleDeleteTodo = async (todoId) => {
    if (window.confirm('Are you sure you want to delete this todo?')) {
      return await deleteTodo(todoId)
    }
  }

  const handleToggleComplete = async (todo) => {
    return await toggleComplete(todo)
  }

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>Simple Todo List</h1>

      {error && (
        <div style={{ background: '#ffebee', color: '#c62828', padding: '10px', margin: '10px 0' }}>
          Error: {error}
          <button onClick={clearError} style={{ float: 'right' }}>×</button>
        </div>
      )}

      <TodoForm onSubmit={handleAddTodo} loading={loading} />

      <h2>Your Todos ({todos.length})</h2>

      {loading && <p>Loading...</p>}

      {todos.length === 0 ? (
        <p>No todos yet. Add one above!</p>
      ) : (
        <div>
          {todos.map((todo) => (
            <TodoCard
              key={todo.id}
              todo={todo}
              onDelete={handleDeleteTodo}
              onToggleComplete={handleToggleComplete}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export default HomePage
