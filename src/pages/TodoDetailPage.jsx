import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { useTodoAPI } from '../hooks/useTodoAPI'
import LoadingSpinner from '../components/LoadingSpinner'

const TodoDetailPage = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const { loading, error, deleteTodo, toggleComplete, clearError } = useTodoAPI()

  const todo = useSelector(state =>
    state.todos.items.find(todo => todo.id.toString() === id)
  )

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this todo?')) {
      const result = await deleteTodo(id)
      if (result.success) {
        navigate('/')
      } else {
        console.error('Failed to delete todo:', result.error)
      }
    }
  }

  const handleToggleComplete = async () => {
    const result = await toggleComplete(todo)
    if (!result.success) {
      console.error('Failed to toggle todo completion:', result.error)
    }
  }

  if (loading && !todo) {
    return <LoadingSpinner text="Loading todo..." />
  }

  if (!todo) {
    return (
      <div style={{ textAlign: 'center', padding: '40px' }}>
        <div style={{ fontSize: '48px' }}>❌</div>
        <h2>Todo Not Found</h2>
        <p>The todo you're looking for doesn't exist.</p>
        <Link to="/" style={{ textDecoration: 'none' }}>
          <button>Back to Home</button>
        </Link>
      </div>
    )
  }

  return (
    <div>
      {/* Navigation */}
      <Link to="/" style={{ textDecoration: 'none', marginBottom: '20px', display: 'inline-block' }}>
        ← Back to Todos
      </Link>

      {/* Error Display */}
      {error && (
        <div style={{ background: '#ffebee', color: '#c62828', padding: '10px', margin: '10px 0', border: '1px solid #ef5350' }}>
          <span>Error: {error}</span>
          <button
            onClick={clearError}
            style={{ float: 'right', background: 'none', border: 'none', cursor: 'pointer' }}
          >
            ×
          </button>
        </div>
      )}

      {/* Todo Detail */}
      <div style={{ border: '1px solid #ccc', padding: '20px' }}>
        <div>
          <h1 style={{ textDecoration: todo.completed ? 'line-through' : 'none' }}>
            {todo.title}
          </h1>
          <div style={{ margin: '10px 0' }}>
            <span style={{ background: '#f0f0f0', padding: '5px 10px', marginRight: '10px', fontSize: '12px' }}>
              {(todo.priority || 'normal').toUpperCase()} Priority
            </span>
            <span style={{ background: todo.completed ? '#e8f5e8' : '#fff3cd', padding: '5px 10px', fontSize: '12px' }}>
              {todo.completed ? 'Completed' : 'Pending'}
            </span>
          </div>
        </div>

        {/* Description */}
        <div style={{ margin: '20px 0' }}>
          <h2>Description</h2>
          <p style={{ textDecoration: todo.completed ? 'line-through' : 'none' }}>
            {todo.description || 'No description provided.'}
          </p>
        </div>

        {/* Details */}
        <div style={{ margin: '20px 0' }}>
          <h2>Details</h2>
          <div style={{ background: '#f8f9fa', padding: '15px' }}>
            <div style={{ marginBottom: '10px' }}>
              <strong>Created At:</strong> {new Date(todo.createdAt).toLocaleString()}
            </div>
            <div style={{ marginBottom: '10px' }}>
              <strong>Status:</strong> {todo.completed ? 'Completed' : 'In Progress'}
            </div>
            <div style={{ marginBottom: '10px' }}>
              <strong>Priority Level:</strong> {(todo.priority || 'Normal').charAt(0).toUpperCase() + (todo.priority || 'Normal').slice(1)}
            </div>
            <div>
              <strong>Todo ID:</strong> #{todo.id}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div style={{ margin: '20px 0' }}>
          <button
            onClick={handleToggleComplete}
            disabled={loading}
            style={{ marginRight: '10px', padding: '10px 15px' }}
          >
            {loading ? 'Updating...' : (todo.completed ? 'Mark as Pending' : 'Mark as Complete')}
          </button>

          <button
            onClick={handleDelete}
            disabled={loading}
            style={{ padding: '10px 15px' }}
          >
            {loading ? 'Deleting...' : 'Delete Todo'}
          </button>
        </div>
      </div>
    </div>
  )
}

export default TodoDetailPage
