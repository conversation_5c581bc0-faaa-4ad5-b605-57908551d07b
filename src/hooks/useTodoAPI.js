import { useDispatch, useSelector } from 'react-redux'
import { useEffect } from 'react'
import {
  fetchTodos,
  addTodo,
  deleteTodo,
  updateTodo,
  clearError
} from '../store/todosSlice'

export const useTodoAPI = () => {
  const dispatch = useDispatch()
  const { items: todos, loading, error } = useSelector(state => state.todos)

  // Fetch todos on mount
  useEffect(() => {
    dispatch(fetchTodos())
  }, [dispatch])

  const handleAddTodo = async (todoData) => {
    try {
      await dispatch(addTodo(todoData)).unwrap()
      return { success: true }
    } catch (error) {
      return { success: false, error }
    }
  }

  const handleDeleteTodo = async (todoId) => {
    try {
      await dispatch(deleteTodo(todoId)).unwrap()
      return { success: true }
    } catch (error) {
      return { success: false, error }
    }
  }

  const handleUpdateTodo = async (id, updates, currentTodo) => {
    try {
      await dispatch(updateTodo({ id, updates, currentTodo })).unwrap()
      return { success: true }
    } catch (error) {
      return { success: false, error }
    }
  }

  const handleToggleComplete = async (todo) => {
    if (!todo || !todo.id) {
      return { success: false, error: 'Invalid todo object' }
    }
    return handleUpdateTodo(todo.id, { completed: !todo.completed }, todo)
  }

  const handleClearError = () => {
    dispatch(clearError())
  }

  const refetchTodos = () => {
    dispatch(fetchTodos())
  }

  return {
    todos,
    loading,
    error,
    addTodo: handleAddTodo,
    deleteTodo: handleDeleteTodo,
    updateTodo: handleUpdateTodo,
    toggleComplete: handleToggleComplete,
    clearError: handleClearError,
    refetchTodos,
  }
}
