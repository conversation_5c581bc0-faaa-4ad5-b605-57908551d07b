import { useDispatch, useSelector } from 'react-redux'
import { useEffect } from 'react'
import { 
  fetchTodos, 
  addTodo, 
  deleteTodo, 
  updateTodo, 
  clearError 
} from '../store/todosSlice'

export const useTodoAPI = () => {
  const dispatch = useDispatch()
  const { items: todos, loading, error } = useSelector(state => state.todos)

  // Fetch todos on mount
  useEffect(() => {
    dispatch(fetchTodos())
  }, [dispatch])

  const handleAddTodo = async (todoData) => {
    try {
      await dispatch(addTodo(todoData)).unwrap()
      return { success: true }
    } catch (error) {
      return { success: false, error }
    }
  }

  const handleDeleteTodo = async (todoId) => {
    try {
      await dispatch(deleteTodo(todoId)).unwrap()
      return { success: true }
    } catch (error) {
      return { success: false, error }
    }
  }

  const handleUpdateTodo = async (id, updates) => {
    try {
      await dispatch(updateTodo({ id, updates })).unwrap()
      return { success: true }
    } catch (error) {
      return { success: false, error }
    }
  }

  const handleToggleComplete = async (todo) => {
    return handleUpdateTodo(todo.id, { completed: !todo.completed })
  }

  const handleClearError = () => {
    dispatch(clearError())
  }

  const refetchTodos = () => {
    dispatch(fetchTodos())
  }

  return {
    todos,
    loading,
    error,
    addTodo: handleAddTodo,
    deleteTodo: handleDeleteTodo,
    updateTodo: handleUpdateTodo,
    toggleComplete: handleToggleComplete,
    clearError: handleClearError,
    refetchTodos,
  }
}
