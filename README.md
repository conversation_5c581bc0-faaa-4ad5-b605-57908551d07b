# Todo List Manager

Ứng dụng quản lý Todo List được xây dựng với <PERSON>act, <PERSON>ux Toolkit, React Router và CSS thuần túy.

## 🚀 Tính năng

- ✅ Thêm, x<PERSON><PERSON>, cập nhật todo
- ✅ Đánh dấu hoàn thành/chưa hoàn thành
- ✅ Phân loại độ ưu tiên (High, Medium, Low)
- ✅ Xem chi tiết từng todo
- ✅ UI đơn giản để test chức năng
- ✅ State management với Redux Toolkit
- ✅ API integration với MockAPI
- ✅ Loading states và error handling

## 🛠️ Công nghệ sử dụng

- **Frontend**: React 19, Vite
- **State Management**: Redux Toolkit, React Redux
- **Routing**: React Router DOM
- **Styling**: Inline styles (minimal styling for testing)
- **API**: MockAPI, Axios
- **Build Tool**: Vite

## 📦 Cài đặt

1. Clone repository:
```bash
git clone <repository-url>
cd to-do-list
```

2. Cài đặt dependencies:
```bash
npm install
```

3. Khởi chạy React app:
```bash
npm run dev
```

## 🌐 URLs

- **React App**: http://localhost:5174
- **API Endpoints**: https://67cd350bdd7651e464eda2a9.mockapi.io/todos

## 📁 Cấu trúc thư mục

```
src/
├── components/          # React components
│   ├── TodoCard.jsx    # Component hiển thị todo card
│   ├── TodoForm.jsx    # Form thêm todo mới
│   └── LoadingSpinner.jsx # Loading spinner component
├── hooks/              # Custom hooks
│   └── useTodoAPI.js   # Hook quản lý API calls
├── pages/              # Page components
│   ├── HomePage.jsx    # Trang chủ hiển thị danh sách todos
│   └── TodoDetailPage.jsx # Trang chi tiết todo
├── store/              # Redux store
│   ├── store.js        # Redux store configuration
│   └── todosSlice.js   # Todos slice với async thunks
├── App.jsx             # Main App component
├── main.jsx           # Entry point
└── index.css          # Basic reset styles only
```

## 🔧 Scripts

- `npm run dev` - Khởi chạy development server
- `npm run build` - Build production
- `npm run preview` - Preview production build

## 📝 API Endpoints

Ứng dụng sử dụng MockAPI để mô phỏng REST API. Tất cả dữ liệu được lưu trữ trên cloud và có thể truy cập từ bất kỳ đâu.

**Base URL**: `https://67cd350bdd7651e464eda2a9.mockapi.io`

### GET /todos
Lấy danh sách tất cả todos

### POST /todos
Thêm todo mới
```json
{
  "title": "Todo title",
  "description": "Todo description",
  "priority": "high|medium|low",
  "completed": false,
  "createdAt": "2024-01-20T10:00:00.000Z"
}
```

### PUT /todos/:id
Cập nhật todo hoàn toàn

### PATCH /todos/:id
Cập nhật một phần todo
```json
{
  "completed": true
}
```

### DELETE /todos/:id
Xóa todo

## 🎯 Cách sử dụng

1. **Thêm Todo**: Điền form bên trái và click "Add Todo"
2. **Xem chi tiết**: Click nút "View" trên todo card
3. **Đánh dấu hoàn thành**: Click nút "Complete" hoặc "Undo"
4. **Xóa Todo**: Click nút "Delete" và xác nhận

## 🔄 Redux State Structure

```javascript
{
  todos: {
    items: [],      // Danh sách todos
    loading: false, // Trạng thái loading
    error: null     // Thông báo lỗi
  }
}
```

## 🎨 UI Features

Ứng dụng sử dụng inline styles tối thiểu để:
- Test chức năng cơ bản
- Hiển thị dữ liệu rõ ràng
- Phân biệt trạng thái completed/pending
- Hiển thị priority levels
- Layout đơn giản với flexbox

## 🚀 Deployment

1. Build production:
```bash
npm run build
```

2. Deploy thư mục `dist` lên hosting service

## 📄 License

MIT License
